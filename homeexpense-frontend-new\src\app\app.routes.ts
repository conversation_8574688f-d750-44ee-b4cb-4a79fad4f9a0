import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full',
  },
  {
    path: 'auth',
    loadChildren: () => import('./features/auth/auth.routes').then((m) => m.authRoutes),
  },
  {
    path: 'dashboard',
    loadChildren: () =>
      import('./features/dashboard/dashboard.routes').then((m) => m.dashboardRoutes),
  },
  {
    path: 'expenses',
    loadChildren: () => import('./features/expenses/expenses.routes').then((m) => m.expensesRoutes),
  },
  {
    path: 'income',
    loadChildren: () => import('./features/income/income.routes').then((m) => m.incomeRoutes),
  },
  {
    path: 'categories',
    loadChildren: () =>
      import('./features/categories/categories.routes').then((m) => m.categoriesRoutes),
  },
  {
    path: 'investments',
    loadChildren: () =>
      import('./features/investments/investments.routes').then((m) => m.investmentsRoutes),
  },
  {
    path: 'banking',
    loadChildren: () => import('./features/banking/banking.routes').then((m) => m.bankingRoutes),
  },
  {
    path: 'refunds',
    loadChildren: () => import('./features/refunds/refunds.routes').then((m) => m.refundsRoutes),
  },
  {
    path: 'analytics',
    loadChildren: () =>
      import('./features/analytics/analytics.routes').then((m) => m.analyticsRoutes),
  },
  {
    path: 'goals',
    loadChildren: () => import('./features/goals/goals.routes').then((m) => m.goalsRoutes),
  },
  {
    path: 'settings',
    loadChildren: () => import('./features/settings/settings.routes').then((m) => m.settingsRoutes),
  },
  {
    path: 'performance',
    loadChildren: () =>
      import('./features/performance/performance.routes').then((m) => m.performanceRoutes),
  },
  {
    path: 'loans',
    loadChildren: () => import('./features/loans/loans.routes').then((m) => m.loansRoutes),
  },
  {
    path: 'fixed-deposits',
    loadChildren: () =>
      import('./features/fixed-deposits/fixed-deposits.routes').then((m) => m.fixedDepositsRoutes),
  },
  {
    path: 'education',
    loadChildren: () =>
      import('./features/education/education.routes').then((m) => m.educationRoutes),
  },
  {
    path: 'orders',
    loadChildren: () => import('./features/orders/orders.routes').then((m) => m.ordersRoutes),
  },
  {
    path: '**',
    redirectTo: '/dashboard',
  },
];
